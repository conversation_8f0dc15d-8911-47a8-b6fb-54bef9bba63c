/*
 * Copyright (c) 2024 5G UPF Project.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 */

#ifndef __included_ikev2_5gc_integration_h__
#define __included_ikev2_5gc_integration_h__

#include <vnet/vnet.h>
#include <vnet/ip/ip.h>
#include <vnet/ipsec/ipsec.h>
#include <ikev2/ikev2.h>
#include <upf/upf.h>

/* 5GC接口类型定义 */
typedef enum {
    UPF_5GC_INTERFACE_N4 = 0,  /* SMF-UPF控制面接口 */
    UPF_5GC_INTERFACE_N3 = 1,  /* gNB-UPF用户面接口 */
    UPF_5GC_INTERFACE_N6 = 2,  /* UPF-DN数据网络接口 */
    UPF_5GC_INTERFACE_MAX
} upf_5gc_interface_type_t;

/* IPsec安全策略配置 */
typedef struct {
    u32 spi_base;                    /* SPI基础值 */
    u32 crypto_alg;                  /* 加密算法 */
    u32 integ_alg;                   /* 完整性算法 */
    u32 key_len;                     /* 密钥长度 */
    u8 *crypto_key;                  /* 加密密钥 */
    u8 *integ_key;                   /* 完整性密钥 */
    ip46_address_t local_addr;       /* 本地地址 */
    ip46_address_t remote_addr;      /* 远程地址 */
    u16 local_port;                  /* 本地端口 */
    u16 remote_port;                 /* 远程端口 */
} upf_ipsec_policy_t;

/* 5GC接口安全上下文 */
typedef struct {
    upf_5gc_interface_type_t interface_type;
    u32 ikev2_profile_index;         /* IKEv2配置文件索引 */
    u32 ipsec_sa_index;              /* IPsec SA索引 */
    upf_ipsec_policy_t policy;       /* 安全策略 */
    u8 is_initiator;                 /* 是否为发起方 */
    u8 sa_established;               /* SA是否已建立 */
    f64 last_activity;               /* 最后活动时间 */
    u32 packets_encrypted;           /* 加密包计数 */
    u32 packets_decrypted;           /* 解密包计数 */
    u32 bytes_encrypted;             /* 加密字节计数 */
    u32 bytes_decrypted;             /* 解密字节计数 */
} upf_5gc_security_context_t;

/* IKEv2-5GC集成主结构 */
typedef struct {
    /* 安全上下文池 */
    upf_5gc_security_context_t *security_contexts;
    
    /* 接口到安全上下文的映射 */
    uword *interface_to_context_index;
    
    /* IKEv2配置 */
    u8 *ikev2_profile_name;
    u32 ikev2_profile_index;
    
    /* 统计信息 */
    u64 total_sa_established;
    u64 total_sa_deleted;
    u64 total_packets_processed;
    u64 total_encryption_errors;
    u64 total_decryption_errors;
    
    /* 配置参数 */
    u32 sa_lifetime;                 /* SA生命周期(秒) */
    u32 rekey_margin;                /* 重新密钥协商边界 */
    u8 auto_negotiate;               /* 自动协商开关 */
    u8 enable_n4_security;           /* N4接口安全开关 */
    u8 enable_n3_security;           /* N3接口安全开关 */
    u8 enable_n6_security;           /* N6接口安全开关 */
    
    /* 便利指针 */
    vlib_main_t *vlib_main;
    vnet_main_t *vnet_main;
    
    /* API消息ID基础 */
    u16 msg_id_base;
    
    /* 日志类 */
    vlib_log_class_t log_class;
} ikev2_5gc_main_t;

extern ikev2_5gc_main_t ikev2_5gc_main;

/* 函数声明 */

/* 初始化和配置 */
clib_error_t *ikev2_5gc_init(vlib_main_t *vm);
int ikev2_5gc_enable_disable(u8 enable);
int ikev2_5gc_configure_interface(upf_5gc_interface_type_t interface_type,
                                  ip46_address_t *local_addr,
                                  ip46_address_t *remote_addr,
                                  u16 local_port, u16 remote_port);

/* 安全上下文管理 */
u32 ikev2_5gc_create_security_context(upf_5gc_interface_type_t interface_type);
int ikev2_5gc_delete_security_context(u32 context_index);
upf_5gc_security_context_t *ikev2_5gc_get_security_context(u32 context_index);

/* IKEv2协商 */
int ikev2_5gc_initiate_negotiation(u32 context_index);
int ikev2_5gc_handle_negotiation_complete(u32 context_index, 
                                          ikev2_sa_t *ikev2_sa);

/* 数据包处理 */
int ikev2_5gc_encrypt_packet(u32 context_index, vlib_buffer_t *b);
int ikev2_5gc_decrypt_packet(u32 context_index, vlib_buffer_t *b);

/* N4接口特定处理 */
int ikev2_5gc_n4_process_pfcp_message(vlib_buffer_t *b, 
                                      upf_5gc_security_context_t *ctx);

/* N3接口特定处理 */
int ikev2_5gc_n3_process_gtpu_message(vlib_buffer_t *b,
                                      upf_5gc_security_context_t *ctx);

/* N6接口特定处理 */
int ikev2_5gc_n6_process_data_message(vlib_buffer_t *b,
                                      upf_5gc_security_context_t *ctx);

/* 统计和监控 */
void ikev2_5gc_update_statistics(u32 context_index, u32 bytes, u8 is_encrypt);
void ikev2_5gc_show_statistics(vlib_main_t *vm);

/* 日志宏 */
#define ikev2_5gc_log_error(...) \
  vlib_log(VLIB_LOG_LEVEL_ERR, ikev2_5gc_main.log_class, __VA_ARGS__)
#define ikev2_5gc_log_warn(...) \
  vlib_log(VLIB_LOG_LEVEL_WARNING, ikev2_5gc_main.log_class, __VA_ARGS__)
#define ikev2_5gc_log_info(...) \
  vlib_log(VLIB_LOG_LEVEL_INFO, ikev2_5gc_main.log_class, __VA_ARGS__)
#define ikev2_5gc_log_debug(...) \
  vlib_log(VLIB_LOG_LEVEL_DEBUG, ikev2_5gc_main.log_class, __VA_ARGS__)

/* 格式化函数 */
u8 *format_upf_5gc_interface_type(u8 *s, va_list *args);
u8 *format_upf_5gc_security_context(u8 *s, va_list *args);

#endif /* __included_ikev2_5gc_integration_h__ */
