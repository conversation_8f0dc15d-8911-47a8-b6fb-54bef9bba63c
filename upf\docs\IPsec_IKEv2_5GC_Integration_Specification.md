# IPsec IKEv2与5G核心网络接口集成技术规范

## 1. 概述

本文档描述了在5G UPF（用户面功能）中集成IPsec IKEv2协议，用于保护N4、N3和N6接口数据传输的技术实现方案。

### 1.1 目标

- 为5G核心网络的N4、N3、N6接口提供端到端的IPsec安全保护
- 实现自动化的IKEv2密钥协商和管理
- 确保与现有UPF功能的无缝集成
- 提供灵活的安全策略配置和管理

### 1.2 适用范围

- N4接口：SMF-UPF控制面PFCP协议保护
- N3接口：gNB-UPF用户面GTP-U隧道保护
- N6接口：UPF-DN数据网络连接保护

## 2. 系统架构

### 2.1 整体架构

```
┌─────────────────────────────────────────────────────────────┐
│                    IPsec IKEv2 Security Layer              │
├─────────────────┬─────────────────┬─────────────────────────┤
│  IKEv2 Manager  │   SA Manager    │    Key Manager          │
├─────────────────┼─────────────────┼─────────────────────────┤
│ N4 Security     │ N3 Security     │ N6 Security             │
│ Module          │ Module          │ Module                  │
├─────────────────┼─────────────────┼─────────────────────────┤
│ PFCP Server     │ GTP-U Processing│ Data Forwarding         │
├─────────────────┴─────────────────┴─────────────────────────┤
│                    IPsec Engine                             │
│              (ESP Encryption/Decryption)                    │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 核心组件

#### 2.2.1 IKEv2-5GC集成管理器
- **文件**: `ikev2_5gc_integration.h/c`
- **功能**: 统一管理5GC接口的IPsec安全上下文
- **职责**: 
  - 安全上下文生命周期管理
  - IKEv2协商触发和监控
  - 统计信息收集和报告

#### 2.2.2 接口特定安全模块
- **N4模块**: `n4_ipsec_handler.c` - PFCP消息安全处理
- **N3模块**: `n3_ipsec_handler.c` - GTP-U隧道安全处理  
- **N6模块**: `n6_ipsec_handler.c` - 数据转发安全处理

## 3. 数据结构设计

### 3.1 安全上下文结构

```c
typedef struct {
    upf_5gc_interface_type_t interface_type;  // 接口类型
    u32 ikev2_profile_index;                  // IKEv2配置文件索引
    u32 ipsec_sa_index;                       // IPsec SA索引
    upf_ipsec_policy_t policy;                // 安全策略
    u8 is_initiator;                          // 发起方标识
    u8 sa_established;                        // SA建立状态
    f64 last_activity;                        // 最后活动时间
    u32 packets_encrypted;                    // 加密包计数
    u32 packets_decrypted;                    // 解密包计数
} upf_5gc_security_context_t;
```

### 3.2 接口特定会话结构

#### N4接口会话
```c
typedef struct {
    u32 context_index;                        // 安全上下文索引
    u32 pfcp_endpoint_index;                  // PFCP端点索引
    ip46_address_t smf_address;               // SMF地址
    u16 pfcp_port;                            // PFCP端口
    u8 security_enabled;                      // 安全启用标识
} n4_ipsec_session_t;
```

#### N3接口隧道
```c
typedef struct {
    u32 context_index;                        // 安全上下文索引
    ip46_address_t gnb_address;               // gNB地址
    u32 teid;                                 // 隧道端点标识符
    u8 security_enabled;                      // 安全启用标识
    f64 last_activity;                        // 最后活动时间
} n3_ipsec_tunnel_t;
```

#### N6接口连接
```c
typedef struct {
    u32 context_index;                        // 安全上下文索引
    ip46_address_t dn_address;                // 数据网络地址
    u32 fib_index;                            // 转发信息库索引
    u8 security_enabled;                      // 安全启用标识
    f64 last_activity;                        // 最后活动时间
} n6_ipsec_connection_t;
```

## 4. 协议流程

### 4.1 IKEv2协商流程

```
发起方                                    响应方
  |                                        |
  |-------- IKE_SA_INIT Request --------->|
  |         (SA, KE, Nonce)                |
  |                                        |
  |<------- IKE_SA_INIT Response ---------|
  |         (SA, KE, Nonce)                |
  |                                        |
  |-------- IKE_AUTH Request ------------>|
  |         (IDi, AUTH, SA, TSi, TSr)      |
  |                                        |
  |<------- IKE_AUTH Response -------------|
  |         (IDr, AUTH, SA, TSi, TSr)      |
  |                                        |
  |-------- ESP SA Installation --------->|
  |                                        |
  |<------- ESP Protected Data ---------->|
```

### 4.2 数据包处理流程

#### 4.2.1 输入数据包处理
1. 接收数据包
2. 检查接口安全配置
3. 识别ESP包并解密
4. 传递给上层协议处理
5. 更新统计信息

#### 4.2.2 输出数据包处理
1. 接收上层协议数据
2. 检查接口安全配置
3. 查找或创建安全上下文
4. 执行ESP加密
5. 发送加密数据包
6. 更新统计信息

## 5. 配置参数

### 5.1 全局配置
```c
typedef struct {
    u32 sa_lifetime;                          // SA生命周期(秒)
    u32 rekey_margin;                         // 重新密钥协商边界
    u8 auto_negotiate;                        // 自动协商开关
    u8 enable_n4_security;                    // N4接口安全开关
    u8 enable_n3_security;                    // N3接口安全开关
    u8 enable_n6_security;                    // N6接口安全开关
} ikev2_5gc_config_t;
```

### 5.2 安全策略配置
```c
typedef struct {
    u32 spi_base;                             // SPI基础值
    u32 crypto_alg;                           // 加密算法
    u32 integ_alg;                            // 完整性算法
    u32 key_len;                              // 密钥长度
    u8 *crypto_key;                           // 加密密钥
    u8 *integ_key;                            // 完整性密钥
    ip46_address_t local_addr;                // 本地地址
    ip46_address_t remote_addr;               // 远程地址
    u16 local_port;                           // 本地端口
    u16 remote_port;                          // 远程端口
} upf_ipsec_policy_t;
```

## 6. API接口

### 6.1 初始化和配置API
```c
clib_error_t *ikev2_5gc_init(vlib_main_t *vm);
int ikev2_5gc_enable_disable(u8 enable);
int ikev2_5gc_configure_interface(upf_5gc_interface_type_t interface_type,
                                  ip46_address_t *local_addr,
                                  ip46_address_t *remote_addr,
                                  u16 local_port, u16 remote_port);
```

### 6.2 安全上下文管理API
```c
u32 ikev2_5gc_create_security_context(upf_5gc_interface_type_t interface_type);
int ikev2_5gc_delete_security_context(u32 context_index);
upf_5gc_security_context_t *ikev2_5gc_get_security_context(u32 context_index);
```

### 6.3 数据包处理API
```c
int ikev2_5gc_encrypt_packet(u32 context_index, vlib_buffer_t *b);
int ikev2_5gc_decrypt_packet(u32 context_index, vlib_buffer_t *b);
```

### 6.4 接口特定处理API
```c
// N4接口
int n4_ipsec_process_pfcp_input(vlib_buffer_t *b, ip46_address_t *smf_addr);
int n4_ipsec_process_pfcp_output(vlib_buffer_t *b, ip46_address_t *smf_addr);

// N3接口
int n3_ipsec_process_gtpu_input(vlib_buffer_t *b, ip46_address_t *gnb_addr, u32 teid);
int n3_ipsec_process_gtpu_output(vlib_buffer_t *b, ip46_address_t *gnb_addr, u32 teid);

// N6接口
int n6_ipsec_process_data_input(vlib_buffer_t *b, ip46_address_t *dn_addr);
int n6_ipsec_process_data_output(vlib_buffer_t *b, ip46_address_t *dn_addr, u32 fib_index);
```

## 7. 错误处理

### 7.1 错误代码定义
```c
#define IKEV2_5GC_SUCCESS                     0
#define IKEV2_5GC_ERROR_INVALID_PARAMETER    -1
#define IKEV2_5GC_ERROR_SA_NOT_ESTABLISHED   -2
#define IKEV2_5GC_ERROR_ENCRYPTION_FAILED    -3
#define IKEV2_5GC_ERROR_DECRYPTION_FAILED    -4
#define IKEV2_5GC_ERROR_CONTEXT_NOT_FOUND    -5
#define IKEV2_5GC_ERROR_NEGOTIATION_FAILED   -6
```

### 7.2 错误处理策略
- 加密/解密失败时记录错误并丢弃数据包
- SA未建立时将数据包排队等待协商完成
- 协商失败时触发重试机制
- 严重错误时禁用相应接口的安全功能

## 8. 性能考虑

### 8.1 优化策略
- 使用硬件加速的加密引擎
- 实现SA缓存机制减少查找开销
- 批量处理数据包提高吞吐量
- 异步处理IKEv2协商避免阻塞数据路径

### 8.2 性能指标
- 加密/解密吞吐量：目标 > 10Gbps
- SA建立时间：目标 < 100ms
- 数据包处理延迟：目标 < 1ms
- 内存使用：每个SA < 1KB

## 9. 安全考虑

### 9.1 密钥管理
- 使用强随机数生成器生成密钥材料
- 定期更新SA密钥（默认1小时）
- 安全删除过期密钥材料

### 9.2 攻击防护
- 实现重放攻击防护
- 限制IKEv2协商频率防止DoS攻击
- 验证数据包完整性防止篡改

### 9.3 审计日志
- 记录所有SA建立和删除事件
- 记录加密/解密错误事件
- 记录安全策略变更事件
