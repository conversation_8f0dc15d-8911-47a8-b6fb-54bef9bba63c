# IPsec IKEv2与5GC接口集成测试策略

## 1. 测试概述

### 1.1 测试目标
- 验证IPsec IKEv2与N4、N3、N6接口的正确集成
- 确保数据传输的安全性和完整性
- 验证系统性能满足5G网络要求
- 确保与现有UPF功能的兼容性

### 1.2 测试范围
- **功能测试**：IPsec协商、加密解密、接口集成
- **性能测试**：吞吐量、延迟、资源使用
- **安全测试**：密钥管理、攻击防护、数据保护
- **兼容性测试**：向后兼容、互操作性
- **稳定性测试**：长时间运行、异常恢复

### 1.3 测试环境
```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│     SMF     │    │     gNB     │    │     DN      │
│             │    │             │    │             │
└──────┬──────┘    └──────┬──────┘    └──────┬──────┘
       │N4                │N3                │N6
       │                  │                  │
       └──────────────────┼──────────────────┘
                          │
                    ┌─────┴─────┐
                    │    UPF    │
                    │ (测试目标) │
                    └───────────┘
```

## 2. 单元测试

### 2.1 IKEv2-5GC集成管理器测试
**测试文件**: `test_ikev2_5gc_integration.c`

```c
// 测试用例1：安全上下文创建和删除
void test_security_context_lifecycle()
{
    u32 ctx_index = ikev2_5gc_create_security_context(UPF_5GC_INTERFACE_N4);
    assert(ctx_index != ~0);
    
    upf_5gc_security_context_t *ctx = ikev2_5gc_get_security_context(ctx_index);
    assert(ctx != NULL);
    assert(ctx->interface_type == UPF_5GC_INTERFACE_N4);
    
    int rv = ikev2_5gc_delete_security_context(ctx_index);
    assert(rv == 0);
}

// 测试用例2：配置接口参数
void test_interface_configuration()
{
    ip46_address_t local_addr, remote_addr;
    ip46_address_set_ip4(&local_addr, &(ip4_address_t){.as_u32 = 0x0100007f}); // 127.0.0.1
    ip46_address_set_ip4(&remote_addr, &(ip4_address_t){.as_u32 = 0x0200007f}); // *********
    
    int rv = ikev2_5gc_configure_interface(UPF_5GC_INTERFACE_N4, 
                                          &local_addr, &remote_addr, 
                                          8805, 8805);
    assert(rv == 0);
}

// 测试用例3：统计信息更新
void test_statistics_update()
{
    u32 ctx_index = ikev2_5gc_create_security_context(UPF_5GC_INTERFACE_N4);
    
    ikev2_5gc_update_statistics(ctx_index, 1500, 1); // 加密1500字节
    ikev2_5gc_update_statistics(ctx_index, 1400, 0); // 解密1400字节
    
    upf_5gc_security_context_t *ctx = ikev2_5gc_get_security_context(ctx_index);
    assert(ctx->bytes_encrypted == 1500);
    assert(ctx->bytes_decrypted == 1400);
    
    ikev2_5gc_delete_security_context(ctx_index);
}
```

### 2.2 N4接口处理器测试
**测试文件**: `test_n4_ipsec_handler.c`

```c
// 测试用例1：PFCP消息加密解密
void test_pfcp_encryption_decryption()
{
    // 创建测试PFCP消息
    vlib_buffer_t *b = create_test_pfcp_message();
    ip46_address_t smf_addr;
    ip46_address_set_ip4(&smf_addr, &(ip4_address_t){.as_u32 = 0x0100007f});
    
    // 测试加密
    int rv = n4_ipsec_encrypt_pfcp_message(b, &smf_addr);
    assert(rv == 0 || rv == -1); // 成功或SA未建立
    
    // 如果加密成功，测试解密
    if (rv == 0) {
        rv = n4_ipsec_decrypt_pfcp_message(b, &smf_addr);
        assert(rv == 0);
    }
    
    vlib_buffer_free_one(vlib_get_main(), vlib_get_buffer_index(vlib_get_main(), b));
}

// 测试用例2：会话管理
void test_n4_session_management()
{
    ip46_address_t smf_addr1, smf_addr2;
    ip46_address_set_ip4(&smf_addr1, &(ip4_address_t){.as_u32 = 0x0100007f});
    ip46_address_set_ip4(&smf_addr2, &(ip4_address_t){.as_u32 = 0x0200007f});
    
    // 创建两个不同的会话
    u32 session1 = n4_ipsec_create_session(&smf_addr1, 8805);
    u32 session2 = n4_ipsec_create_session(&smf_addr2, 8805);
    
    assert(session1 != session2);
    
    // 验证会话查找
    n4_ipsec_session_t *s1 = n4_ipsec_find_session(&smf_addr1);
    n4_ipsec_session_t *s2 = n4_ipsec_find_session(&smf_addr2);
    
    assert(s1 != NULL && s2 != NULL);
    assert(s1 != s2);
}
```

### 2.3 N3接口处理器测试
**测试文件**: `test_n3_ipsec_handler.c`

```c
// 测试用例1：GTP-U消息处理
void test_gtpu_processing()
{
    vlib_buffer_t *b = create_test_gtpu_message();
    ip46_address_t gnb_addr;
    ip46_address_set_ip4(&gnb_addr, &(ip4_address_t){.as_u32 = 0x0100007f});
    u32 teid = 0x12345678;
    
    // 测试输入处理
    int rv = n3_ipsec_process_gtpu_input(b, &gnb_addr, teid);
    assert(rv == 0); // 应该直接通过（安全未启用或非ESP包）
    
    // 测试输出处理
    rv = n3_ipsec_process_gtpu_output(b, &gnb_addr, teid);
    assert(rv == 0 || rv == -1); // 成功或SA未建立
    
    vlib_buffer_free_one(vlib_get_main(), vlib_get_buffer_index(vlib_get_main(), b));
}

// 测试用例2：隧道管理
void test_n3_tunnel_management()
{
    ip46_address_t gnb_addr;
    ip46_address_set_ip4(&gnb_addr, &(ip4_address_t){.as_u32 = 0x0100007f});
    u32 teid1 = 0x12345678;
    u32 teid2 = 0x87654321;
    
    // 创建隧道
    u32 tunnel1 = n3_ipsec_create_tunnel(&gnb_addr, teid1);
    u32 tunnel2 = n3_ipsec_create_tunnel(&gnb_addr, teid2);
    
    // 验证隧道查找
    n3_ipsec_tunnel_t *t1 = n3_ipsec_find_tunnel_by_teid(teid1);
    n3_ipsec_tunnel_t *t2 = n3_ipsec_find_tunnel_by_teid(teid2);
    
    assert(t1 != NULL && t2 != NULL);
    assert(t1->teid == teid1 && t2->teid == teid2);
}
```

## 3. 集成测试

### 3.1 N4接口端到端测试
**测试场景**: SMF与UPF之间的PFCP会话建立和管理

```bash
#!/bin/bash
# test_n4_integration.sh

echo "启动N4接口集成测试..."

# 1. 启用N4接口安全
vppctl "ikev2 5gc enable n4"

# 2. 配置N4接口参数
vppctl "ikev2 5gc configure n4 local ************* remote ************* port 8805"

# 3. 发送PFCP会话建立请求
python3 send_pfcp_session_establishment.py --smf-ip ************* --upf-ip *************

# 4. 验证IPsec SA建立
vppctl "show n4 ipsec"

# 5. 发送PFCP会话修改请求
python3 send_pfcp_session_modification.py --smf-ip ************* --upf-ip *************

# 6. 验证加密统计
vppctl "show n4 ipsec statistics"

echo "N4接口集成测试完成"
```

### 3.2 N3接口端到端测试
**测试场景**: gNB与UPF之间的GTP-U隧道数据传输

```bash
#!/bin/bash
# test_n3_integration.sh

echo "启动N3接口集成测试..."

# 1. 启用N3接口安全
vppctl "ikev2 5gc enable n3"

# 2. 配置N3接口参数
vppctl "ikev2 5gc configure n3 local ************* remote ************* port 2152"

# 3. 发送GTP-U数据包
python3 send_gtpu_data.py --gnb-ip ************* --upf-ip ************* --teid 0x12345678

# 4. 验证IPsec SA建立
vppctl "show n3 ipsec"

# 5. 发送大量数据包测试
python3 send_gtpu_bulk_data.py --gnb-ip ************* --upf-ip ************* --count 1000

# 6. 验证加密统计
vppctl "show n3 ipsec statistics"

echo "N3接口集成测试完成"
```

### 3.3 N6接口端到端测试
**测试场景**: UPF与数据网络之间的数据转发

```bash
#!/bin/bash
# test_n6_integration.sh

echo "启动N6接口集成测试..."

# 1. 启用N6接口安全
vppctl "ikev2 5gc enable n6"

# 2. 配置N6接口参数
vppctl "ikev2 5gc configure n6 local ************* remote ************* port 0"

# 3. 发送上行数据包（UE -> DN）
python3 send_uplink_data.py --ue-ip ********** --dn-ip *************

# 4. 发送下行数据包（DN -> UE）
python3 send_downlink_data.py --dn-ip ************* --ue-ip **********

# 5. 验证IPsec SA建立
vppctl "show n6 ipsec"

# 6. 验证加密统计
vppctl "show n6 ipsec statistics"

echo "N6接口集成测试完成"
```

## 4. 性能测试

### 4.1 吞吐量测试
**目标**: 验证IPsec加密不会显著影响数据吞吐量

```python
#!/usr/bin/env python3
# throughput_test.py

import time
import threading
from scapy.all import *

class ThroughputTest:
    def __init__(self, interface, target_ip, duration=60):
        self.interface = interface
        self.target_ip = target_ip
        self.duration = duration
        self.packet_count = 0
        self.byte_count = 0
        
    def send_packets(self):
        """发送数据包"""
        start_time = time.time()
        while time.time() - start_time < self.duration:
            # 创建1500字节的测试包
            pkt = IP(dst=self.target_ip)/UDP()/Raw(b'A' * 1472)
            send(pkt, iface=self.interface, verbose=0)
            self.packet_count += 1
            self.byte_count += len(pkt)
            
    def run_test(self):
        """运行吞吐量测试"""
        print(f"开始吞吐量测试，持续时间：{self.duration}秒")
        
        start_time = time.time()
        self.send_packets()
        end_time = time.time()
        
        actual_duration = end_time - start_time
        throughput_pps = self.packet_count / actual_duration
        throughput_bps = self.byte_count * 8 / actual_duration
        
        print(f"测试结果：")
        print(f"  数据包数量：{self.packet_count}")
        print(f"  总字节数：{self.byte_count}")
        print(f"  吞吐量：{throughput_pps:.2f} pps")
        print(f"  吞吐量：{throughput_bps/1000000:.2f} Mbps")

if __name__ == "__main__":
    # 测试N3接口吞吐量
    test = ThroughputTest("eth0", "*************", 60)
    test.run_test()
```

### 4.2 延迟测试
**目标**: 测量IPsec处理引入的额外延迟

```python
#!/usr/bin/env python3
# latency_test.py

import time
import statistics
from scapy.all import *

class LatencyTest:
    def __init__(self, interface, target_ip, count=1000):
        self.interface = interface
        self.target_ip = target_ip
        self.count = count
        self.latencies = []
        
    def measure_latency(self):
        """测量单个数据包的延迟"""
        # 发送ICMP Echo请求
        pkt = IP(dst=self.target_ip)/ICMP()
        start_time = time.time()
        
        # 发送并等待回复
        reply = sr1(pkt, iface=self.interface, timeout=1, verbose=0)
        
        if reply:
            end_time = time.time()
            latency = (end_time - start_time) * 1000  # 转换为毫秒
            return latency
        else:
            return None
            
    def run_test(self):
        """运行延迟测试"""
        print(f"开始延迟测试，发送{self.count}个数据包")
        
        for i in range(self.count):
            latency = self.measure_latency()
            if latency is not None:
                self.latencies.append(latency)
                
            if (i + 1) % 100 == 0:
                print(f"已完成 {i + 1}/{self.count}")
                
        if self.latencies:
            avg_latency = statistics.mean(self.latencies)
            min_latency = min(self.latencies)
            max_latency = max(self.latencies)
            std_latency = statistics.stdev(self.latencies)
            
            print(f"延迟测试结果：")
            print(f"  平均延迟：{avg_latency:.2f} ms")
            print(f"  最小延迟：{min_latency:.2f} ms")
            print(f"  最大延迟：{max_latency:.2f} ms")
            print(f"  标准差：{std_latency:.2f} ms")
        else:
            print("延迟测试失败：没有收到回复")

if __name__ == "__main__":
    test = LatencyTest("eth0", "*************", 1000)
    test.run_test()
```

## 5. 安全测试

### 5.1 密钥管理测试
```bash
#!/bin/bash
# security_test.sh

echo "开始安全测试..."

# 1. 测试IKEv2协商
echo "测试IKEv2协商..."
vppctl "ikev2 5gc initiate n4 *************"

# 2. 验证SA建立
echo "验证SA建立..."
vppctl "show ipsec sa"

# 3. 测试密钥更新
echo "测试密钥更新..."
sleep 3600  # 等待1小时触发重新协商
vppctl "show ipsec sa"

# 4. 测试SA删除
echo "测试SA删除..."
vppctl "ikev2 5gc delete-sa n4 *************"
vppctl "show ipsec sa"

echo "安全测试完成"
```

### 5.2 攻击防护测试
```python
#!/usr/bin/env python3
# attack_test.py

from scapy.all import *
import time

def replay_attack_test(target_ip, interface):
    """重放攻击测试"""
    print("开始重放攻击测试...")
    
    # 捕获一个ESP包
    esp_pkt = sniff(iface=interface, filter=f"esp and host {target_ip}", count=1)[0]
    
    # 重放相同的包多次
    for i in range(10):
        send(esp_pkt, iface=interface, verbose=0)
        time.sleep(0.1)
        
    print("重放攻击测试完成")

def dos_attack_test(target_ip, interface):
    """DoS攻击测试"""
    print("开始DoS攻击测试...")
    
    # 发送大量IKE_SA_INIT请求
    for i in range(1000):
        pkt = IP(dst=target_ip)/UDP(dport=500)/Raw(b'\x00' * 100)
        send(pkt, iface=interface, verbose=0)
        
    print("DoS攻击测试完成")

if __name__ == "__main__":
    target_ip = "*************"
    interface = "eth0"
    
    replay_attack_test(target_ip, interface)
    dos_attack_test(target_ip, interface)
```

## 6. 自动化测试框架

### 6.1 测试执行脚本
```bash
#!/bin/bash
# run_all_tests.sh

TEST_DIR="$(dirname "$0")"
LOG_DIR="$TEST_DIR/logs"
RESULT_FILE="$LOG_DIR/test_results.txt"

mkdir -p "$LOG_DIR"
echo "IPsec IKEv2 5GC集成测试报告" > "$RESULT_FILE"
echo "测试时间: $(date)" >> "$RESULT_FILE"
echo "================================" >> "$RESULT_FILE"

# 运行单元测试
echo "运行单元测试..." | tee -a "$RESULT_FILE"
cd "$TEST_DIR/unit_tests"
for test in test_*.c; do
    echo "执行 $test..." | tee -a "$RESULT_FILE"
    gcc -o "${test%.c}" "$test" -lvppinfra -lvlib -lupf
    if ./"${test%.c}"; then
        echo "  ✓ 通过" | tee -a "$RESULT_FILE"
    else
        echo "  ✗ 失败" | tee -a "$RESULT_FILE"
    fi
done

# 运行集成测试
echo "运行集成测试..." | tee -a "$RESULT_FILE"
cd "$TEST_DIR/integration_tests"
for test in test_*.sh; do
    echo "执行 $test..." | tee -a "$RESULT_FILE"
    if bash "$test"; then
        echo "  ✓ 通过" | tee -a "$RESULT_FILE"
    else
        echo "  ✗ 失败" | tee -a "$RESULT_FILE"
    fi
done

# 运行性能测试
echo "运行性能测试..." | tee -a "$RESULT_FILE"
cd "$TEST_DIR/performance_tests"
python3 throughput_test.py | tee -a "$RESULT_FILE"
python3 latency_test.py | tee -a "$RESULT_FILE"

# 运行安全测试
echo "运行安全测试..." | tee -a "$RESULT_FILE"
cd "$TEST_DIR/security_tests"
bash security_test.sh | tee -a "$RESULT_FILE"
python3 attack_test.py | tee -a "$RESULT_FILE"

echo "所有测试完成，结果保存在 $RESULT_FILE"
```

## 7. 测试环境配置

### 7.1 测试拓扑配置
```yaml
# test_topology.yaml
topology:
  nodes:
    - name: smf
      type: simulator
      ip: *************
      interfaces:
        - name: n4
          ip: *************
          port: 8805
          
    - name: gnb
      type: simulator  
      ip: *************
      interfaces:
        - name: n3
          ip: *************
          port: 2152
          
    - name: dn
      type: simulator
      ip: *************
      interfaces:
        - name: n6
          ip: *************
          
    - name: upf
      type: target
      ip: *************
      interfaces:
        - name: n4
          ip: *************
          port: 8805
        - name: n3
          ip: *************
          port: 2152
        - name: n6
          ip: *************

connections:
  - from: smf.n4
    to: upf.n4
    type: pfcp
    
  - from: gnb.n3
    to: upf.n3
    type: gtpu
    
  - from: upf.n6
    to: dn.n6
    type: ip
```

### 7.2 测试数据准备
```python
#!/usr/bin/env python3
# prepare_test_data.py

import json
import random

def generate_pfcp_sessions(count=100):
    """生成PFCP会话测试数据"""
    sessions = []
    for i in range(count):
        session = {
            "session_id": f"session_{i:04d}",
            "smf_ip": f"192.168.1.{200 + i % 50}",
            "ue_ip": f"10.0.{i//256}.{i%256}",
            "qos_profile": random.choice(["5qi_1", "5qi_7", "5qi_9"]),
            "bandwidth": random.randint(1, 100) * 1000000  # 1-100 Mbps
        }
        sessions.append(session)
    
    with open("pfcp_sessions.json", "w") as f:
        json.dump(sessions, f, indent=2)
    
    print(f"生成了 {count} 个PFCP会话测试数据")

def generate_gtpu_tunnels(count=200):
    """生成GTP-U隧道测试数据"""
    tunnels = []
    for i in range(count):
        tunnel = {
            "teid": f"0x{i:08x}",
            "gnb_ip": f"192.168.2.{200 + i % 50}",
            "ue_ip": f"10.0.{i//256}.{i%256}",
            "qfi": random.randint(1, 63)
        }
        tunnels.append(tunnel)
    
    with open("gtpu_tunnels.json", "w") as f:
        json.dump(tunnels, f, indent=2)
    
    print(f"生成了 {count} 个GTP-U隧道测试数据")

if __name__ == "__main__":
    generate_pfcp_sessions(100)
    generate_gtpu_tunnels(200)
```

## 8. 测试报告模板

### 8.1 测试结果汇总
```
IPsec IKEv2与5GC接口集成测试报告

测试执行时间：2024-XX-XX
测试环境：开发环境
测试版本：v1.0.0

测试结果汇总：
┌─────────────────┬─────────┬─────────┬─────────┬─────────┐
│    测试类型     │ 总数量  │  通过   │  失败   │ 通过率  │
├─────────────────┼─────────┼─────────┼─────────┼─────────┤
│    单元测试     │   15    │   14    │    1    │  93.3%  │
│    集成测试     │    9    │    8    │    1    │  88.9%  │
│    性能测试     │    6    │    5    │    1    │  83.3%  │
│    安全测试     │    4    │    4    │    0    │ 100.0%  │
├─────────────────┼─────────┼─────────┼─────────┼─────────┤
│      总计       │   34    │   31    │    3    │  91.2%  │
└─────────────────┴─────────┴─────────┴─────────┴─────────┘

性能指标：
- N4接口吞吐量：8.5 Gbps (目标: >10 Gbps) ❌
- N3接口吞吐量：12.3 Gbps (目标: >10 Gbps) ✅
- N6接口吞吐量：11.8 Gbps (目标: >10 Gbps) ✅
- 平均延迟：0.8 ms (目标: <1 ms) ✅
- SA建立时间：85 ms (目标: <100 ms) ✅

主要问题：
1. N4接口性能未达到目标要求
2. 单元测试中安全上下文删除测试失败
3. 集成测试中N6接口异常恢复测试失败

建议：
1. 优化N4接口的PFCP消息处理性能
2. 修复安全上下文生命周期管理问题
3. 完善N6接口的错误处理机制

详细测试日志请参考：
- 单元测试日志：logs/unit_test.log
- 集成测试日志：logs/integration_test.log
- 性能测试日志：logs/performance_test.log
- 安全测试日志：logs/security_test.log
```
