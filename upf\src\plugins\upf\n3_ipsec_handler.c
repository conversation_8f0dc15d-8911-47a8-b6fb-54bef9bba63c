/*
 * Copyright (c) 2024 5G UPF Project.
 * N3接口IPsec处理模块
 * 集成IPsec IKEv2与GTP-U协议处理
 */

#include <vnet/vnet.h>
#include <vnet/plugin/plugin.h>
#include <vnet/ipsec/ipsec.h>
#include <vnet/udp/udp.h>
#include <upf/upf.h>
#include <upf/ikev2_5gc_integration.h>
#include <gtpu/gtpu.h>

typedef struct {
    u32 context_index;
    ip46_address_t gnb_address;
    u32 teid;
    u8 security_enabled;
    f64 last_activity;
} n3_ipsec_tunnel_t;

typedef struct {
    /* N3隧道池 */
    n3_ipsec_tunnel_t *tunnels;
    
    /* gNB地址到隧道的映射 */
    uword *gnb_to_tunnel_index;
    
    /* TEID到隧道的映射 */
    uword *teid_to_tunnel_index;
    
    /* 统计信息 */
    u64 gtpu_packets_encrypted;
    u64 gtpu_packets_decrypted;
    u64 gtpu_encryption_errors;
    u64 gtpu_decryption_errors;
    
    /* 便利指针 */
    vlib_main_t *vlib_main;
    ikev2_5gc_main_t *ikev2_5gc_main;
} n3_ipsec_main_t;

n3_ipsec_main_t n3_ipsec_main;

/* N3 IPsec隧道创建 */
static u32
n3_ipsec_create_tunnel(ip46_address_t *gnb_addr, u32 teid)
{
    n3_ipsec_main_t *nm = &n3_ipsec_main;
    n3_ipsec_tunnel_t *tunnel;
    u32 tunnel_index;
    
    pool_get_aligned(nm->tunnels, tunnel, CLIB_CACHE_LINE_BYTES);
    tunnel_index = tunnel - nm->tunnels;
    
    clib_memset(tunnel, 0, sizeof(*tunnel));
    tunnel->gnb_address = *gnb_addr;
    tunnel->teid = teid;
    tunnel->last_activity = vlib_time_now(nm->vlib_main);
    
    /* 创建对应的5GC安全上下文 */
    tunnel->context_index = ikev2_5gc_create_security_context(UPF_5GC_INTERFACE_N3);
    
    /* 建立映射关系 */
    hash_set_mem(nm->gnb_to_tunnel_index, gnb_addr, tunnel_index);
    hash_set(nm->teid_to_tunnel_index, teid, tunnel_index);
    
    ikev2_5gc_log_info("N3 IPsec tunnel created for gNB %U, TEID=0x%x, tunnel_index=%u",
                       format_ip46_address, gnb_addr, IP46_TYPE_ANY, teid, tunnel_index);
    
    return tunnel_index;
}

/* N3 IPsec隧道查找 */
static n3_ipsec_tunnel_t *
n3_ipsec_find_tunnel_by_gnb(ip46_address_t *gnb_addr)
{
    n3_ipsec_main_t *nm = &n3_ipsec_main;
    uword *p;
    
    p = hash_get_mem(nm->gnb_to_tunnel_index, gnb_addr);
    if (!p)
        return NULL;
        
    return pool_elt_at_index(nm->tunnels, p[0]);
}

static n3_ipsec_tunnel_t *
n3_ipsec_find_tunnel_by_teid(u32 teid)
{
    n3_ipsec_main_t *nm = &n3_ipsec_main;
    uword *p;
    
    p = hash_get(nm->teid_to_tunnel_index, teid);
    if (!p)
        return NULL;
        
    return pool_elt_at_index(nm->tunnels, p[0]);
}

/* GTP-U消息加密处理 */
int
n3_ipsec_encrypt_gtpu_message(vlib_buffer_t *b, ip46_address_t *gnb_addr, u32 teid)
{
    n3_ipsec_main_t *nm = &n3_ipsec_main;
    n3_ipsec_tunnel_t *tunnel;
    upf_5gc_security_context_t *ctx;
    int rv;
    
    /* 查找或创建N3隧道 */
    tunnel = n3_ipsec_find_tunnel_by_gnb(gnb_addr);
    if (!tunnel) {
        tunnel = pool_elt_at_index(nm->tunnels, 
                                  n3_ipsec_create_tunnel(gnb_addr, teid));
        
        /* 启动IKEv2协商 */
        rv = ikev2_5gc_initiate_negotiation(tunnel->context_index);
        if (rv != 0) {
            ikev2_5gc_log_error("Failed to initiate IKEv2 negotiation for N3 interface");
            return rv;
        }
    }
    
    /* 更新活动时间 */
    tunnel->last_activity = vlib_time_now(nm->vlib_main);
    
    /* 获取安全上下文 */
    ctx = ikev2_5gc_get_security_context(tunnel->context_index);
    if (!ctx || !ctx->sa_established) {
        ikev2_5gc_log_debug("N3 IPsec SA not established yet, queuing packet");
        return -1;
    }
    
    /* 执行IPsec加密 */
    rv = ikev2_5gc_encrypt_packet(tunnel->context_index, b);
    if (rv == 0) {
        nm->gtpu_packets_encrypted++;
        ikev2_5gc_update_statistics(tunnel->context_index, 
                                   vlib_buffer_length_in_chain(nm->vlib_main, b), 1);
    } else {
        nm->gtpu_encryption_errors++;
        ikev2_5gc_log_error("N3 GTP-U message encryption failed");
    }
    
    return rv;
}

/* GTP-U消息解密处理 */
int
n3_ipsec_decrypt_gtpu_message(vlib_buffer_t *b, ip46_address_t *gnb_addr, u32 teid)
{
    n3_ipsec_main_t *nm = &n3_ipsec_main;
    n3_ipsec_tunnel_t *tunnel;
    upf_5gc_security_context_t *ctx;
    int rv;
    
    /* 查找N3隧道 */
    tunnel = n3_ipsec_find_tunnel_by_teid(teid);
    if (!tunnel) {
        ikev2_5gc_log_error("N3 IPsec tunnel not found for TEID 0x%x", teid);
        return -1;
    }
    
    /* 更新活动时间 */
    tunnel->last_activity = vlib_time_now(nm->vlib_main);
    
    /* 获取安全上下文 */
    ctx = ikev2_5gc_get_security_context(tunnel->context_index);
    if (!ctx || !ctx->sa_established) {
        ikev2_5gc_log_error("N3 IPsec SA not established");
        return -1;
    }
    
    /* 执行IPsec解密 */
    rv = ikev2_5gc_decrypt_packet(tunnel->context_index, b);
    if (rv == 0) {
        nm->gtpu_packets_decrypted++;
        ikev2_5gc_update_statistics(tunnel->context_index,
                                   vlib_buffer_length_in_chain(nm->vlib_main, b), 0);
    } else {
        nm->gtpu_decryption_errors++;
        ikev2_5gc_log_error("N3 GTP-U message decryption failed");
    }
    
    return rv;
}

/* 集成到现有GTP-U处理流程 */
int
n3_ipsec_process_gtpu_input(vlib_buffer_t *b, ip46_address_t *gnb_addr, u32 teid)
{
    ikev2_5gc_main_t *ikm = &ikev2_5gc_main;
    
    /* 检查N3接口安全是否启用 */
    if (!ikm->enable_n3_security) {
        return 0; /* 直接通过，不进行IPsec处理 */
    }
    
    /* 检查是否为ESP包 */
    ip4_header_t *ip4 = vlib_buffer_get_current(b);
    if (ip4->protocol == IP_PROTOCOL_IPSEC_ESP) {
        /* 这是一个ESP包，需要解密 */
        return n3_ipsec_decrypt_gtpu_message(b, gnb_addr, teid);
    }
    
    return 0;
}

int
n3_ipsec_process_gtpu_output(vlib_buffer_t *b, ip46_address_t *gnb_addr, u32 teid)
{
    ikev2_5gc_main_t *ikm = &ikev2_5gc_main;
    
    /* 检查N3接口安全是否启用 */
    if (!ikm->enable_n3_security) {
        return 0; /* 直接通过，不进行IPsec处理 */
    }
    
    /* 对输出的GTP-U消息进行加密 */
    return n3_ipsec_encrypt_gtpu_message(b, gnb_addr, teid);
}

/* 显示N3 IPsec统计信息 */
static clib_error_t *
show_n3_ipsec_command_fn(vlib_main_t *vm, unformat_input_t *input,
                         vlib_cli_command_t *cmd)
{
    n3_ipsec_main_t *nm = &n3_ipsec_main;
    n3_ipsec_tunnel_t *tunnel;
    
    vlib_cli_output(vm, "N3 IPsec Statistics:");
    vlib_cli_output(vm, "  GTP-U packets encrypted: %llu", nm->gtpu_packets_encrypted);
    vlib_cli_output(vm, "  GTP-U packets decrypted: %llu", nm->gtpu_packets_decrypted);
    vlib_cli_output(vm, "  Encryption errors: %llu", nm->gtpu_encryption_errors);
    vlib_cli_output(vm, "  Decryption errors: %llu", nm->gtpu_decryption_errors);
    vlib_cli_output(vm, "");
    
    vlib_cli_output(vm, "Active N3 IPsec Tunnels:");
    pool_foreach(tunnel, nm->tunnels, ({
        upf_5gc_security_context_t *ctx = ikev2_5gc_get_security_context(tunnel->context_index);
        vlib_cli_output(vm, "  gNB: %U, TEID: 0x%x, SA established: %s",
                       format_ip46_address, &tunnel->gnb_address, IP46_TYPE_ANY,
                       tunnel->teid,
                       ctx && ctx->sa_established ? "Yes" : "No");
    }));
    
    return 0;
}

VLIB_CLI_COMMAND(show_n3_ipsec_command, static) = {
    .path = "show n3 ipsec",
    .short_help = "show n3 ipsec",
    .function = show_n3_ipsec_command_fn,
};

/* N3 IPsec模块初始化 */
clib_error_t *
n3_ipsec_init(vlib_main_t *vm)
{
    n3_ipsec_main_t *nm = &n3_ipsec_main;
    
    nm->vlib_main = vm;
    nm->ikev2_5gc_main = &ikev2_5gc_main;
    nm->gnb_to_tunnel_index = hash_create_mem(0, sizeof(ip46_address_t), sizeof(uword));
    nm->teid_to_tunnel_index = hash_create(0, sizeof(uword));
    
    ikev2_5gc_log_info("N3 IPsec handler initialized");
    
    return 0;
}

VLIB_INIT_FUNCTION(n3_ipsec_init);
