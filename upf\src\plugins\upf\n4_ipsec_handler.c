/*
 * Copyright (c) 2024 5G UPF Project.
 * N4接口IPsec处理模块
 * 集成IPsec IKEv2与PFCP协议处理
 */

#include <vnet/vnet.h>
#include <vnet/plugin/plugin.h>
#include <vnet/ipsec/ipsec.h>
#include <vnet/udp/udp.h>
#include <upf/upf.h>
#include <upf/upf_pfcp.h>
#include <upf/ikev2_5gc_integration.h>

typedef struct {
    u32 context_index;
    u32 pfcp_endpoint_index;
    ip46_address_t smf_address;
    u16 pfcp_port;
    u8 security_enabled;
} n4_ipsec_session_t;

typedef struct {
    /* N4会话池 */
    n4_ipsec_session_t *sessions;
    
    /* SMF地址到会话的映射 */
    uword *smf_to_session_index;
    
    /* 统计信息 */
    u64 pfcp_packets_encrypted;
    u64 pfcp_packets_decrypted;
    u64 pfcp_encryption_errors;
    u64 pfcp_decryption_errors;
    
    /* 便利指针 */
    vlib_main_t *vlib_main;
    ikev2_5gc_main_t *ikev2_5gc_main;
} n4_ipsec_main_t;

n4_ipsec_main_t n4_ipsec_main;

/* N4 IPsec会话创建 */
static u32
n4_ipsec_create_session(ip46_address_t *smf_addr, u16 pfcp_port)
{
    n4_ipsec_main_t *nm = &n4_ipsec_main;
    n4_ipsec_session_t *session;
    u32 session_index;
    
    pool_get_aligned(nm->sessions, session, CLIB_CACHE_LINE_BYTES);
    session_index = session - nm->sessions;
    
    clib_memset(session, 0, sizeof(*session));
    session->smf_address = *smf_addr;
    session->pfcp_port = pfcp_port;
    
    /* 创建对应的5GC安全上下文 */
    session->context_index = ikev2_5gc_create_security_context(UPF_5GC_INTERFACE_N4);
    
    /* 建立映射关系 */
    hash_set_mem(nm->smf_to_session_index, smf_addr, session_index);
    
    ikev2_5gc_log_info("N4 IPsec session created for SMF %U:%d, session_index=%u",
                       format_ip46_address, smf_addr, IP46_TYPE_ANY, pfcp_port, session_index);
    
    return session_index;
}

/* N4 IPsec会话查找 */
static n4_ipsec_session_t *
n4_ipsec_find_session(ip46_address_t *smf_addr)
{
    n4_ipsec_main_t *nm = &n4_ipsec_main;
    uword *p;
    
    p = hash_get_mem(nm->smf_to_session_index, smf_addr);
    if (!p)
        return NULL;
        
    return pool_elt_at_index(nm->sessions, p[0]);
}

/* PFCP消息加密处理 */
int
n4_ipsec_encrypt_pfcp_message(vlib_buffer_t *b, ip46_address_t *smf_addr)
{
    n4_ipsec_main_t *nm = &n4_ipsec_main;
    n4_ipsec_session_t *session;
    upf_5gc_security_context_t *ctx;
    int rv;
    
    /* 查找或创建N4会话 */
    session = n4_ipsec_find_session(smf_addr);
    if (!session) {
        session = pool_elt_at_index(nm->sessions, 
                                   n4_ipsec_create_session(smf_addr, PFCP_UDP_DST_PORT));
        
        /* 启动IKEv2协商 */
        rv = ikev2_5gc_initiate_negotiation(session->context_index);
        if (rv != 0) {
            ikev2_5gc_log_error("Failed to initiate IKEv2 negotiation for N4 interface");
            return rv;
        }
    }
    
    /* 获取安全上下文 */
    ctx = ikev2_5gc_get_security_context(session->context_index);
    if (!ctx || !ctx->sa_established) {
        ikev2_5gc_log_debug("N4 IPsec SA not established yet, queuing packet");
        /* 这里可以实现包队列机制，等待SA建立完成 */
        return -1;
    }
    
    /* 执行IPsec加密 */
    rv = ikev2_5gc_encrypt_packet(session->context_index, b);
    if (rv == 0) {
        nm->pfcp_packets_encrypted++;
        ikev2_5gc_update_statistics(session->context_index, 
                                   vlib_buffer_length_in_chain(nm->vlib_main, b), 1);
    } else {
        nm->pfcp_encryption_errors++;
        ikev2_5gc_log_error("N4 PFCP message encryption failed");
    }
    
    return rv;
}

/* PFCP消息解密处理 */
int
n4_ipsec_decrypt_pfcp_message(vlib_buffer_t *b, ip46_address_t *smf_addr)
{
    n4_ipsec_main_t *nm = &n4_ipsec_main;
    n4_ipsec_session_t *session;
    upf_5gc_security_context_t *ctx;
    int rv;
    
    /* 查找N4会话 */
    session = n4_ipsec_find_session(smf_addr);
    if (!session) {
        ikev2_5gc_log_error("N4 IPsec session not found for SMF %U",
                           format_ip46_address, smf_addr, IP46_TYPE_ANY);
        return -1;
    }
    
    /* 获取安全上下文 */
    ctx = ikev2_5gc_get_security_context(session->context_index);
    if (!ctx || !ctx->sa_established) {
        ikev2_5gc_log_error("N4 IPsec SA not established");
        return -1;
    }
    
    /* 执行IPsec解密 */
    rv = ikev2_5gc_decrypt_packet(session->context_index, b);
    if (rv == 0) {
        nm->pfcp_packets_decrypted++;
        ikev2_5gc_update_statistics(session->context_index,
                                   vlib_buffer_length_in_chain(nm->vlib_main, b), 0);
    } else {
        nm->pfcp_decryption_errors++;
        ikev2_5gc_log_error("N4 PFCP message decryption failed");
    }
    
    return rv;
}

/* 集成到现有PFCP处理流程 */
int
n4_ipsec_process_pfcp_input(vlib_buffer_t *b, ip46_address_t *smf_addr)
{
    ikev2_5gc_main_t *ikm = &ikev2_5gc_main;
    
    /* 检查N4接口安全是否启用 */
    if (!ikm->enable_n4_security) {
        return 0; /* 直接通过，不进行IPsec处理 */
    }
    
    /* 检查是否为ESP包 */
    ip4_header_t *ip4 = vlib_buffer_get_current(b);
    if (ip4->protocol == IP_PROTOCOL_IPSEC_ESP) {
        /* 这是一个ESP包，需要解密 */
        return n4_ipsec_decrypt_pfcp_message(b, smf_addr);
    }
    
    return 0;
}

int
n4_ipsec_process_pfcp_output(vlib_buffer_t *b, ip46_address_t *smf_addr)
{
    ikev2_5gc_main_t *ikm = &ikev2_5gc_main;
    
    /* 检查N4接口安全是否启用 */
    if (!ikm->enable_n4_security) {
        return 0; /* 直接通过，不进行IPsec处理 */
    }
    
    /* 对输出的PFCP消息进行加密 */
    return n4_ipsec_encrypt_pfcp_message(b, smf_addr);
}

/* 显示N4 IPsec统计信息 */
static clib_error_t *
show_n4_ipsec_command_fn(vlib_main_t *vm, unformat_input_t *input,
                         vlib_cli_command_t *cmd)
{
    n4_ipsec_main_t *nm = &n4_ipsec_main;
    n4_ipsec_session_t *session;
    
    vlib_cli_output(vm, "N4 IPsec Statistics:");
    vlib_cli_output(vm, "  PFCP packets encrypted: %llu", nm->pfcp_packets_encrypted);
    vlib_cli_output(vm, "  PFCP packets decrypted: %llu", nm->pfcp_packets_decrypted);
    vlib_cli_output(vm, "  Encryption errors: %llu", nm->pfcp_encryption_errors);
    vlib_cli_output(vm, "  Decryption errors: %llu", nm->pfcp_decryption_errors);
    vlib_cli_output(vm, "");
    
    vlib_cli_output(vm, "Active N4 IPsec Sessions:");
    pool_foreach(session, nm->sessions, ({
        upf_5gc_security_context_t *ctx = ikev2_5gc_get_security_context(session->context_index);
        vlib_cli_output(vm, "  SMF: %U:%d, SA established: %s",
                       format_ip46_address, &session->smf_address, IP46_TYPE_ANY,
                       session->pfcp_port,
                       ctx && ctx->sa_established ? "Yes" : "No");
    }));
    
    return 0;
}

VLIB_CLI_COMMAND(show_n4_ipsec_command, static) = {
    .path = "show n4 ipsec",
    .short_help = "show n4 ipsec",
    .function = show_n4_ipsec_command_fn,
};

/* N4 IPsec模块初始化 */
clib_error_t *
n4_ipsec_init(vlib_main_t *vm)
{
    n4_ipsec_main_t *nm = &n4_ipsec_main;
    
    nm->vlib_main = vm;
    nm->ikev2_5gc_main = &ikev2_5gc_main;
    nm->smf_to_session_index = hash_create_mem(0, sizeof(ip46_address_t), sizeof(uword));
    
    ikev2_5gc_log_info("N4 IPsec handler initialized");
    
    return 0;
}

VLIB_INIT_FUNCTION(n4_ipsec_init);
