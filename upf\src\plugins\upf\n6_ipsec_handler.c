/*
 * Copyright (c) 2024 5G UPF Project.
 * N6接口IPsec处理模块
 * 集成IPsec IKEv2与数据网络转发处理
 */

#include <vnet/vnet.h>
#include <vnet/plugin/plugin.h>
#include <vnet/ipsec/ipsec.h>
#include <vnet/ip/ip.h>
#include <upf/upf.h>
#include <upf/ikev2_5gc_integration.h>

typedef struct {
    u32 context_index;
    ip46_address_t dn_address;
    u32 fib_index;
    u8 security_enabled;
    f64 last_activity;
} n6_ipsec_connection_t;

typedef struct {
    /* N6连接池 */
    n6_ipsec_connection_t *connections;
    
    /* DN地址到连接的映射 */
    uword *dn_to_connection_index;
    
    /* 统计信息 */
    u64 data_packets_encrypted;
    u64 data_packets_decrypted;
    u64 data_encryption_errors;
    u64 data_decryption_errors;
    
    /* 便利指针 */
    vlib_main_t *vlib_main;
    ikev2_5gc_main_t *ikev2_5gc_main;
} n6_ipsec_main_t;

n6_ipsec_main_t n6_ipsec_main;

/* N6 IPsec连接创建 */
static u32
n6_ipsec_create_connection(ip46_address_t *dn_addr, u32 fib_index)
{
    n6_ipsec_main_t *nm = &n6_ipsec_main;
    n6_ipsec_connection_t *conn;
    u32 conn_index;
    
    pool_get_aligned(nm->connections, conn, CLIB_CACHE_LINE_BYTES);
    conn_index = conn - nm->connections;
    
    clib_memset(conn, 0, sizeof(*conn));
    conn->dn_address = *dn_addr;
    conn->fib_index = fib_index;
    conn->last_activity = vlib_time_now(nm->vlib_main);
    
    /* 创建对应的5GC安全上下文 */
    conn->context_index = ikev2_5gc_create_security_context(UPF_5GC_INTERFACE_N6);
    
    /* 建立映射关系 */
    hash_set_mem(nm->dn_to_connection_index, dn_addr, conn_index);
    
    ikev2_5gc_log_info("N6 IPsec connection created for DN %U, fib_index=%u, conn_index=%u",
                       format_ip46_address, dn_addr, IP46_TYPE_ANY, fib_index, conn_index);
    
    return conn_index;
}

/* N6 IPsec连接查找 */
static n6_ipsec_connection_t *
n6_ipsec_find_connection(ip46_address_t *dn_addr)
{
    n6_ipsec_main_t *nm = &n6_ipsec_main;
    uword *p;
    
    p = hash_get_mem(nm->dn_to_connection_index, dn_addr);
    if (!p)
        return NULL;
        
    return pool_elt_at_index(nm->connections, p[0]);
}

/* 数据包加密处理 */
int
n6_ipsec_encrypt_data_packet(vlib_buffer_t *b, ip46_address_t *dn_addr, u32 fib_index)
{
    n6_ipsec_main_t *nm = &n6_ipsec_main;
    n6_ipsec_connection_t *conn;
    upf_5gc_security_context_t *ctx;
    int rv;
    
    /* 查找或创建N6连接 */
    conn = n6_ipsec_find_connection(dn_addr);
    if (!conn) {
        conn = pool_elt_at_index(nm->connections, 
                                n6_ipsec_create_connection(dn_addr, fib_index));
        
        /* 启动IKEv2协商 */
        rv = ikev2_5gc_initiate_negotiation(conn->context_index);
        if (rv != 0) {
            ikev2_5gc_log_error("Failed to initiate IKEv2 negotiation for N6 interface");
            return rv;
        }
    }
    
    /* 更新活动时间 */
    conn->last_activity = vlib_time_now(nm->vlib_main);
    
    /* 获取安全上下文 */
    ctx = ikev2_5gc_get_security_context(conn->context_index);
    if (!ctx || !ctx->sa_established) {
        ikev2_5gc_log_debug("N6 IPsec SA not established yet, queuing packet");
        return -1;
    }
    
    /* 执行IPsec加密 */
    rv = ikev2_5gc_encrypt_packet(conn->context_index, b);
    if (rv == 0) {
        nm->data_packets_encrypted++;
        ikev2_5gc_update_statistics(conn->context_index, 
                                   vlib_buffer_length_in_chain(nm->vlib_main, b), 1);
    } else {
        nm->data_encryption_errors++;
        ikev2_5gc_log_error("N6 data packet encryption failed");
    }
    
    return rv;
}

/* 数据包解密处理 */
int
n6_ipsec_decrypt_data_packet(vlib_buffer_t *b, ip46_address_t *dn_addr)
{
    n6_ipsec_main_t *nm = &n6_ipsec_main;
    n6_ipsec_connection_t *conn;
    upf_5gc_security_context_t *ctx;
    int rv;
    
    /* 查找N6连接 */
    conn = n6_ipsec_find_connection(dn_addr);
    if (!conn) {
        ikev2_5gc_log_error("N6 IPsec connection not found for DN %U",
                           format_ip46_address, dn_addr, IP46_TYPE_ANY);
        return -1;
    }
    
    /* 更新活动时间 */
    conn->last_activity = vlib_time_now(nm->vlib_main);
    
    /* 获取安全上下文 */
    ctx = ikev2_5gc_get_security_context(conn->context_index);
    if (!ctx || !ctx->sa_established) {
        ikev2_5gc_log_error("N6 IPsec SA not established");
        return -1;
    }
    
    /* 执行IPsec解密 */
    rv = ikev2_5gc_decrypt_packet(conn->context_index, b);
    if (rv == 0) {
        nm->data_packets_decrypted++;
        ikev2_5gc_update_statistics(conn->context_index,
                                   vlib_buffer_length_in_chain(nm->vlib_main, b), 0);
    } else {
        nm->data_decryption_errors++;
        ikev2_5gc_log_error("N6 data packet decryption failed");
    }
    
    return rv;
}

/* 集成到现有数据转发流程 */
int
n6_ipsec_process_data_input(vlib_buffer_t *b, ip46_address_t *dn_addr)
{
    ikev2_5gc_main_t *ikm = &ikev2_5gc_main;
    
    /* 检查N6接口安全是否启用 */
    if (!ikm->enable_n6_security) {
        return 0; /* 直接通过，不进行IPsec处理 */
    }
    
    /* 检查是否为ESP包 */
    ip4_header_t *ip4 = vlib_buffer_get_current(b);
    if (ip4->protocol == IP_PROTOCOL_IPSEC_ESP) {
        /* 这是一个ESP包，需要解密 */
        return n6_ipsec_decrypt_data_packet(b, dn_addr);
    }
    
    return 0;
}

int
n6_ipsec_process_data_output(vlib_buffer_t *b, ip46_address_t *dn_addr, u32 fib_index)
{
    ikev2_5gc_main_t *ikm = &ikev2_5gc_main;
    
    /* 检查N6接口安全是否启用 */
    if (!ikm->enable_n6_security) {
        return 0; /* 直接通过，不进行IPsec处理 */
    }
    
    /* 对输出的数据包进行加密 */
    return n6_ipsec_encrypt_data_packet(b, dn_addr, fib_index);
}

/* 显示N6 IPsec统计信息 */
static clib_error_t *
show_n6_ipsec_command_fn(vlib_main_t *vm, unformat_input_t *input,
                         vlib_cli_command_t *cmd)
{
    n6_ipsec_main_t *nm = &n6_ipsec_main;
    n6_ipsec_connection_t *conn;
    
    vlib_cli_output(vm, "N6 IPsec Statistics:");
    vlib_cli_output(vm, "  Data packets encrypted: %llu", nm->data_packets_encrypted);
    vlib_cli_output(vm, "  Data packets decrypted: %llu", nm->data_packets_decrypted);
    vlib_cli_output(vm, "  Encryption errors: %llu", nm->data_encryption_errors);
    vlib_cli_output(vm, "  Decryption errors: %llu", nm->data_decryption_errors);
    vlib_cli_output(vm, "");
    
    vlib_cli_output(vm, "Active N6 IPsec Connections:");
    pool_foreach(conn, nm->connections, ({
        upf_5gc_security_context_t *ctx = ikev2_5gc_get_security_context(conn->context_index);
        vlib_cli_output(vm, "  DN: %U, FIB: %u, SA established: %s",
                       format_ip46_address, &conn->dn_address, IP46_TYPE_ANY,
                       conn->fib_index,
                       ctx && ctx->sa_established ? "Yes" : "No");
    }));
    
    return 0;
}

VLIB_CLI_COMMAND(show_n6_ipsec_command, static) = {
    .path = "show n6 ipsec",
    .short_help = "show n6 ipsec",
    .function = show_n6_ipsec_command_fn,
};

/* N6 IPsec模块初始化 */
clib_error_t *
n6_ipsec_init(vlib_main_t *vm)
{
    n6_ipsec_main_t *nm = &n6_ipsec_main;
    
    nm->vlib_main = vm;
    nm->ikev2_5gc_main = &ikev2_5gc_main;
    nm->dn_to_connection_index = hash_create_mem(0, sizeof(ip46_address_t), sizeof(uword));
    
    ikev2_5gc_log_info("N6 IPsec handler initialized");
    
    return 0;
}

VLIB_INIT_FUNCTION(n6_ipsec_init);
